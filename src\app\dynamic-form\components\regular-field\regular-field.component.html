<!-- Regular Field (non-grouped, non-multi) - EXACT from main component lines 54-198 - FormGroup Added -->
@if (field.fieldName && field.fieldName.trim()) {
<div class="form-field" [formGroup]="form">
  <label [for]="field.fieldName">{{ field.label || field.fieldName }} @if (field.mandatory) {<span>*</span>}
@if (field.noInput) {<span class="no-input-indicator"> (Read Only)</span>}
 </label>

    @if (field.foreginKey && getFormControl(field.fieldName)) {
         <app-dropdown
          [fieldName]="field.fieldName"
          [formControl]="getFormControl(field.fieldName)"
          [config]="getRegularDropdownConfig(field)"
          [isDisabled]="isViewMode || field.noInput"
          [isReadonly]="isViewMode || field.noInput"
          [fields]="fields"
          [inputId]="getUniqueFieldId(field.fieldName)"
          (valueChange)="onDropdownValueChange($event)">
        </app-dropdown>
    } @else if (field.lookUp && getFormControl(field.fieldName)) {
         <app-dropdown
          [fieldName]="field.fieldName"
          [formControl]="getFormControl(field.fieldName)"
          [config]="getLookupDropdownConfig(field)"
          [isDisabled]="isViewMode || field.noInput"
          [isReadonly]="isViewMode || field.noInput"
          [fields]="fields"
          [inputId]="getUniqueFieldId(field.fieldName)"
          [options]="field.lookupOptions"
          (valueChange)="onDropdownValueChange($event)">
        </app-dropdown>
    } @else {
      <!-- Regular input fields for non-foreign key fields -->
      @if (field.type === 'boolean') {
        <input [formControlName]="field.fieldName" [id]="field.fieldName"
          type="checkbox" [readonly]="isViewMode || field.noInput" />
      }
      @if (field.type === 'string') {
        <input class="form-input" [formControlName]="field.fieldName" [id]="field.fieldName"
          type="text" [readonly]="isViewMode || field.noInput" [placeholder]="(field.label?.trim() || field.fieldName)" />
      }
      @if (field.type === 'int') {
        <input class="form-input" [formControlName]="field.fieldName" [id]="field.fieldName"
          type="number" [readonly]="isViewMode || field.noInput" />
      }
      @if (field.type === 'date') {
        <input class="form-input" [formControlName]="field.fieldName" [id]="field.fieldName"
          type="date" [readonly]="isViewMode || field.noInput" />
      }
      @if (field.type === 'double') {
        <input class="form-input" [formControlName]="field.fieldName" [id]="field.fieldName"
          type="number" step="00.50" [readonly]="isViewMode || field.noInput" />
      }
    }
  </div>
}
